import type { AddRecordType } from './schema'
import { format } from 'date-fns'
import { useEffect } from 'react'
import FormMessage from '~/components/common/form-message'
import { Card, CardContent, CardFooter } from '~/components/ui/card'
import { Input } from '~/components/ui/input'
import { Label } from '~/components/ui/label'
import { useAppForm } from '~/hooks/form'
import { addRecordSchema } from './schema'
import { baptismaFormOpts, documentFormOpts, inneihFormOpts } from './shared-form'
import useAddRecord from './use-add-record'

interface Props {
  categoryId: string
  categoryName: string
}

type FormType = 'baptisma' | 'inneih' | 'document'

function getFormType(categoryName: string): FormType {
  if (categoryName.includes('Baptisma Record'))
    return 'baptisma'
  if (categoryName.includes('Inneih Record'))
    return 'inneih'
  return 'document'
}

function getDefaultValues(formType: FormType, categoryId?: string) {
  const today = format(new Date(), 'yyyy-MM-dd')
  const baseDocument = { category_id: categoryId, added_date: today }

  switch (formType) {
    case 'baptisma':
      return {
        ...baptismaFormOpts,
        document: { ...baptismaFormOpts.document, ...baseDocument },
      }
    case 'inneih':
      return {
        ...inneihFormOpts,
        document: { ...inneihFormOpts.document, ...baseDocument },
      }
    default:
      return {
        ...documentFormOpts,
        document: { ...documentFormOpts.document, ...baseDocument },
      }
  }
}

export default function DocumentForm({ categoryId, categoryName }: Props) {
  const formType = getFormType(categoryName)
  const defaultValues = getDefaultValues(formType, categoryId)

  const { addRecord } = useAddRecord()

  const form = useAppForm({
    defaultValues: {
      ...defaultValues,
    } as AddRecordType,
    validators: {
      onSubmit: addRecordSchema,
    },
    onSubmit: async ({ value }) => {
      await addRecord.mutateAsync({
        data: {
          ...value,
        },
        categoryName,
      }, {
        onSuccess: () => {
          form.reset()
        },
      })
    },
  })

  useEffect(() => {
    form.reset() // reset form when the category is changed from parent
  }, [categoryName, form])

  return (
    <form onSubmit={(e) => {
      e.preventDefault()
      e.stopPropagation()
      form.handleSubmit()
    }}
    >
      <Card className="w-full max-w-256">
        <CardContent className="flex flex-col gap-4">
          {/* Conditional record fields based on category */}
          {formType === 'baptisma' && (
            <>
              <h3 className="text-lg font-semibold">Baptisma Record</h3>
              <div className="flex gap-x-4">
                <div className="w-full">
                  <form.AppField
                    name="baptisma_record.hming"
                    children={field => <field.InputField label="Hming" />}
                  />
                </div>
                <div className="w-full">
                  <form.AppField
                    name="baptisma_record.khua"
                    children={field => <field.InputField label="Khua" />}
                  />
                </div>
              </div>
              <div className="flex gap-x-4">
                <div className="w-full">
                  <form.AppField
                    name="baptisma_record.pa_hming"
                    children={field => <field.InputField label="Pa hming" />}
                  />
                </div>
                <div className="w-full">
                  <form.AppField
                    name="baptisma_record.nu_hming"
                    children={field => <field.InputField label="Nu hming" />}
                  />
                </div>
              </div>
              <div className="flex gap-x-4">
                <div className="w-full">
                  <form.AppField
                    name="baptisma_record.pian_ni"
                    children={field => <field.InputField label="Pian ni" type="date" />}
                  />
                </div>
                <div className="w-full">
                  <form.AppField
                    name="baptisma_record.baptisma_chan_ni"
                    children={field => <field.InputField label="Baptisma Chan ni" type="date" />}
                  />
                </div>
              </div>
              <div className="flex gap-x-4">
                <div className="w-full">
                  <form.AppField
                    name="baptisma_record.pian_ni_remarks"
                    children={field => <field.TextareaField label="Pian ni remarks" />}
                  />
                </div>
                <div className="w-full">
                  <form.AppField
                    name="baptisma_record.baptisma_chan_ni_remarks"
                    children={field => <field.TextareaField label="Baptisma chan ni remarks" />}
                  />
                </div>
              </div>
              <div className="flex gap-x-4">
                <div className="basis-1/2">
                  <form.AppField
                    name="baptisma_record.chantirtu"
                    children={field => <field.InputField label="Baptisma chantirtu" />}
                  />
                </div>
                <div className="basis-1/2">
                  <form.AppField
                    name="baptisma_record.registration_no"
                    children={field => <field.InputField label="Registration No" />}
                  />
                </div>
              </div>
              <div className="my-2 border" />
            </>
          )}

          {formType === 'inneih' && (
            <>
              <h3 className="text-lg font-semibold">Inneih Record</h3>
              <div className="flex gap-x-4">
                <div className="w-full">
                  <form.AppField
                    name="inneih_record.mipa_hming"
                    children={field => <field.InputField label="Mipa Hming" />}
                  />
                </div>
                <div className="w-full">
                  <form.AppField
                    name="inneih_record.mipa_pa_hming"
                    children={field => <field.InputField label="Mipa Pa Hming" />}
                  />
                </div>
              </div>
              <div className="flex gap-x-4">
                <div className="w-full">
                  <form.AppField
                    name="inneih_record.hmeichhe_hming"
                    children={field => <field.InputField label="Hmeichhe Hming" />}
                  />
                </div>
                <div className="w-full">
                  <form.AppField
                    name="inneih_record.hmeichhe_pa_hming"
                    children={field => <field.InputField label="Hmeichhe Pa Hming" />}
                  />
                </div>
              </div>
              <div className="flex gap-x-4">
                <div className="w-full">
                  <form.AppField
                    name="inneih_record.mipa_khua"
                    children={field => <field.InputField label="Mipa Khua" />}
                  />
                </div>
                <div className="w-full">
                  <form.AppField
                    name="inneih_record.hmeichhe_khua"
                    children={field => <field.InputField label="Hmeichhe Khua" />}
                  />
                </div>
              </div>
              <div className="flex gap-x-4">
                <div className="w-full">
                  <form.AppField
                    name="inneih_record.hmun"
                    children={field => <field.InputField label="Inneihna hmun" />}
                  />
                </div>
                <div className="w-full">
                  <form.AppField
                    name="inneih_record.inneihtirtu"
                    children={field => <field.InputField label="Inneih tir tu" />}
                  />
                </div>
              </div>
              <div className="flex gap-x-4">
                <div className="basis-1/2">
                  <form.AppField
                    name="inneih_record.inneih_ni"
                    children={field => <field.InputField label="Inneihni" type="date" />}
                  />
                </div>
                <div className="basis-1/2">
                  <form.AppField
                    name="inneih_record.registration_no"
                    children={field => <field.InputField label="Registration No" />}
                  />
                </div>
              </div>
              <div className="my-2 border" />
            </>
          )}

          {/* Document fields are always present */}
          <h3 className="text-lg font-semibold">Document Information</h3>
          <div className="flex gap-x-4">
            <div className="w-full">
              <form.AppField
                name="document.title"
                children={field => <field.InputField label="Title" />}
              />
            </div>
            <div className="w-full">
              <form.AppField
                name="document.tags"
                children={field => <field.PillInput label="Tags" />}
              />
            </div>
          </div>
          <div className="w-full">
            <form.AppField
              name="document.body"
              children={field => <field.InputRichText label="Body" />}
            />
          </div>
          <div className="flex gap-x-4">
            <div className="w-full">
              <form.Field
                name="document.files"
                children={field => (
                  <Label className="flex flex-col items-start gap-y-2">
                    <div>Upload file</div>
                    <Input
                      type="file"
                      multiple={true}
                      accept="image/*,application/pdf"
                      onChange={(e) => {
                        if (e.target.files && e.target.files.length > 0) {
                          field.handleChange(Array.from(e.target.files))
                        }
                        else {
                          field.handleChange([])
                        }
                      }}
                      className="bg-white"
                    />

                    <FormMessage errors={field.state.meta.errors.map(e => e?.message).join(', ')} />
                  </Label>
                )}
              />
            </div>
            <div className="w-full">
              <form.AppField
                name="document.added_date"
                children={field => <field.InputField type="date" label="Added date" />}
              />
            </div>
          </div>
          <div>
            <form.AppField
              name="document.is_classified"
              children={field => <field.CheckboxField label="Classified" />}
            />
          </div>
        </CardContent>

        <CardFooter>
          <form.AppForm>
            <form.SubmitButton label="Submit" />
          </form.AppForm>
        </CardFooter>
      </Card>
    </form>
  )
}
